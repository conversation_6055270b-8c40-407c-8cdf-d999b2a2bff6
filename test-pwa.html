<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .pending { background-color: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body>
    <h1>HK Transit Hub PWA Tests</h1>
    
    <div id="manifest-test" class="test pending">
        <h3>Manifest Test</h3>
        <p id="manifest-result">Testing...</p>
    </div>
    
    <div id="sw-test" class="test pending">
        <h3>Service Worker Test</h3>
        <p id="sw-result">Testing...</p>
    </div>
    
    <div id="cors-test" class="test pending">
        <h3>CORS/API Test</h3>
        <p id="cors-result">Testing...</p>
    </div>
    
    <div id="install-test" class="test pending">
        <h3>Install Prompt Test</h3>
        <p id="install-result">Testing...</p>
    </div>

    <script>
        // Test manifest
        async function testManifest() {
            try {
                const response = await fetch('/manifest.webmanifest');
                if (response.ok) {
                    const manifest = await response.json();
                    document.getElementById('manifest-result').textContent = 
                        `✓ Manifest loaded: ${manifest.name}`;
                    document.getElementById('manifest-test').className = 'test success';
                } else {
                    throw new Error('Manifest not found');
                }
            } catch (error) {
                document.getElementById('manifest-result').textContent = 
                    `✗ Manifest error: ${error.message}`;
                document.getElementById('manifest-test').className = 'test error';
            }
        }

        // Test service worker
        async function testServiceWorker() {
            try {
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        document.getElementById('sw-result').textContent = 
                            `✓ Service Worker registered: ${registration.scope}`;
                        document.getElementById('sw-test').className = 'test success';
                    } else {
                        document.getElementById('sw-result').textContent = 
                            '⚠ Service Worker not registered yet (normal in dev mode)';
                        document.getElementById('sw-test').className = 'test pending';
                    }
                } else {
                    throw new Error('Service Worker not supported');
                }
            } catch (error) {
                document.getElementById('sw-result').textContent = 
                    `✗ Service Worker error: ${error.message}`;
                document.getElementById('sw-test').className = 'test error';
            }
        }

        // Test CORS/API
        async function testCORS() {
            try {
                const response = await fetch('/api/kmb/route/');
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('cors-result').textContent = 
                        `✓ API accessible: ${data.data ? data.data.length : 0} routes loaded`;
                    document.getElementById('cors-test').className = 'test success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('cors-result').textContent = 
                    `✗ API error: ${error.message}`;
                document.getElementById('cors-test').className = 'test error';
            }
        }

        // Test install prompt
        function testInstallPrompt() {
            if ('serviceWorker' in navigator && 'PushManager' in window) {
                window.addEventListener('beforeinstallprompt', (e) => {
                    document.getElementById('install-result').textContent = 
                        '✓ Install prompt available';
                    document.getElementById('install-test').className = 'test success';
                });
                
                // If no prompt after 2 seconds, show status
                setTimeout(() => {
                    if (document.getElementById('install-test').className === 'test pending') {
                        document.getElementById('install-result').textContent = 
                            '⚠ Install prompt not triggered (may already be installed or not supported)';
                    }
                }, 2000);
            } else {
                document.getElementById('install-result').textContent = 
                    '✗ PWA features not supported';
                document.getElementById('install-test').className = 'test error';
            }
        }

        // Run tests
        testManifest();
        testServiceWorker();
        testCORS();
        testInstallPrompt();
    </script>
</body>
</html>
